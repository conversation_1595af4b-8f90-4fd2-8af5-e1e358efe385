package com.mohamedrady.v2hoor.firebase

import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.UserProfileChangeRequest
import com.google.firebase.database.DatabaseReference
import com.google.firebase.database.FirebaseDatabase
import com.mohamedrady.v2hoor.handler.MmkvManager
import kotlinx.coroutines.tasks.await

/**
 * Firebase Authentication Manager
 */
object FirebaseAuthManager {
    private const val TAG = "FirebaseAuthManager"
    
    private val auth: FirebaseAuth by lazy { FirebaseAuth.getInstance() }
    private val database: DatabaseReference by lazy {
        FirebaseDatabase.getInstance("https://mrelfeky-209615-default-rtdb.firebaseio.com/").reference
    }
    
    /**
     * Check if user is logged in
     */
    fun isLoggedIn(): Boolean {
        return auth.currentUser != null
    }
    
    /**
     * Get current user
     */
    fun getCurrentUser(): FirebaseUser? {
        return auth.currentUser
    }
    
    /**
     * Get current user ID
     */
    fun getCurrentUserId(): String? {
        return auth.currentUser?.uid
    }
    
    /**
     * Sign in with email and password (alias for compatibility)
     */
    suspend fun signIn(email: String, password: String): AuthResult {
        return signInWithEmailAndPassword(email, password)
    }

    /**
     * Sign in with email and password
     */
    suspend fun signInWithEmailAndPassword(email: String, password: String): AuthResult {
        return try {
            val result = auth.signInWithEmailAndPassword(email, password).await()
            val user = result.user
            
            if (user != null) {
                // Update last login time
                updateLastLoginTime(user.uid)
                
                // Save login state
                MmkvManager.encode("remember_me", true)
                MmkvManager.encode("user_email", email)
                
                Log.d(TAG, "User signed in successfully: ${user.email}")
                AuthResult.Success(user)
            } else {
                AuthResult.Error("فشل في تسجيل الدخول")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Sign in failed", e)
            AuthResult.Error(getErrorMessage(e))
        }
    }
    
    /**
     * Sign up with email and password (alias for compatibility)
     */
    suspend fun signUp(email: String, password: String, username: String, displayName: String): AuthResult {
        return createUserWithEmailAndPassword(email, password, displayName)
    }

    /**
     * Create user with email and password
     */
    suspend fun createUserWithEmailAndPassword(
        email: String,
        password: String,
        displayName: String
    ): AuthResult {
        return try {
            val result = auth.createUserWithEmailAndPassword(email, password).await()
            val user = result.user
            
            if (user != null) {
                // Update user profile
                val profileUpdates = UserProfileChangeRequest.Builder()
                    .setDisplayName(displayName)
                    .build()
                user.updateProfile(profileUpdates).await()
                
                // Create user record in database
                createUserRecord(user, displayName)
                
                Log.d(TAG, "User created successfully: ${user.email}")
                AuthResult.Success(user)
            } else {
                AuthResult.Error("فشل في إنشاء الحساب")
            }
        } catch (e: Exception) {
            Log.e(TAG, "User creation failed", e)
            AuthResult.Error(getErrorMessage(e))
        }
    }
    
    /**
     * Sign out user
     */
    fun signOut(): Boolean {
        return try {
            auth.signOut()
            
            // Clear saved login state
            MmkvManager.encode("remember_me", false)
            MmkvManager.encode("user_email", "")
            MmkvManager.encode("guest_mode", false)
            
            Log.d(TAG, "User signed out successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Sign out failed", e)
            false
        }
    }
    
    /**
     * Send password reset email
     */
    suspend fun sendPasswordResetEmail(email: String): Boolean {
        return try {
            auth.sendPasswordResetEmail(email).await()
            Log.d(TAG, "Password reset email sent to: $email")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send password reset email", e)
            false
        }
    }
    
    /**
     * Check if current user is admin
     */
    suspend fun isCurrentUserAdmin(): Boolean {
        val user = getCurrentUser() ?: return false
        return isUserAdmin(user.uid)
    }
    
    /**
     * Check if user is admin by UID
     */
    suspend fun isUserAdmin(uid: String): Boolean {
        return try {
            val snapshot = database.child("users").child(uid).child("isAdmin").get().await()
            snapshot.getValue(Boolean::class.java) ?: false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking admin status", e)
            // Fallback: check if email is admin email
            val user = auth.currentUser
            user?.email == "<EMAIL>"
        }
    }
    
    /**
     * Get user servers
     */
    suspend fun getUserServers(userId: String): List<FirebaseServerModel> {
        return try {
            FirebaseServerManager.getUserServers(userId)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user servers", e)
            emptyList()
        }
    }
    
    /**
     * Create user record in database
     */
    private suspend fun createUserRecord(user: FirebaseUser, displayName: String) {
        try {
            val userModel = FirebaseUserModel(
                uid = user.uid,
                email = user.email ?: "",
                username = user.email?.substringBefore("@") ?: "",
                displayName = displayName,
                isActive = true,
                isAdmin = user.email == "<EMAIL>",
                role = if (user.email == "<EMAIL>") "admin" else "user",
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
                lastLoginAt = System.currentTimeMillis()
            )
            
            database.child("users").child(user.uid).setValue(userModel).await()
            Log.d(TAG, "User record created in database")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create user record", e)
        }
    }
    
    /**
     * Update last login time
     */
    private suspend fun updateLastLoginTime(uid: String) {
        try {
            val updates = mapOf(
                "lastLoginAt" to System.currentTimeMillis(),
                "updatedAt" to System.currentTimeMillis()
            )
            database.child("users").child(uid).updateChildren(updates).await()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update last login time", e)
        }
    }
    
    /**
     * Get error message from exception
     */
    private fun getErrorMessage(exception: Exception): String {
        return when {
            exception.message?.contains("network") == true -> "خطأ في الاتصال بالإنترنت"
            exception.message?.contains("password") == true -> "كلمة المرور غير صحيحة"
            exception.message?.contains("email") == true -> "البريد الإلكتروني غير صحيح"
            exception.message?.contains("user-not-found") == true -> "المستخدم غير موجود"
            exception.message?.contains("wrong-password") == true -> "كلمة المرور غير صحيحة"
            exception.message?.contains("email-already-in-use") == true -> "البريد الإلكتروني مستخدم بالفعل"
            exception.message?.contains("weak-password") == true -> "كلمة المرور ضعيفة"
            exception.message?.contains("invalid-email") == true -> "البريد الإلكتروني غير صالح"
            else -> exception.message ?: "حدث خطأ غير متوقع"
        }
    }
    
    /**
     * Authentication result sealed class
     */
    sealed class AuthResult {
        data class Success(val user: FirebaseUser) : AuthResult() {
            val uid: String get() = user.uid
        }
        data class Error(val message: String) : AuthResult()
    }
}
