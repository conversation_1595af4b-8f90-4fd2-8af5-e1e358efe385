package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityAdminPanelBinding
import com.mohamedrady.v2hoor.firebase.FirebaseAuthManager
import com.mohamedrady.v2hoor.firebase.FirebaseUserManager
import com.mohamedrady.v2hoor.utils.SuperAdminManager
import com.mohamedrady.v2hoor.ui.adapter.UserActionListener
import com.mohamedrady.v2hoor.firebase.FirebaseUserModel
import com.mohamedrady.v2hoor.ui.adapter.AdminUsersAdapter
import com.mohamedrady.v2hoor.util.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Admin Panel Activity for managing users and subscriptions
 */
class AdminPanelActivity : BaseActivity(), UserActionListener {
    private val binding by lazy { ActivityAdminPanelBinding.inflate(layoutInflater) }
    private val usersAdapter by lazy { AdminUsersAdapter(this) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        
        setupToolbar()
        setupRecyclerView()
        setupListeners()
        checkAdminAccess()
        loadDashboardData()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            title = getString(R.string.admin_panel)
        }
    }

    private fun setupRecyclerView() {
        binding.recyclerViewUsers.apply {
            layoutManager = LinearLayoutManager(this@AdminPanelActivity)
            adapter = usersAdapter
        }
    }

    private fun setupListeners() {
        // إدارة المستخدمين
        binding.cardUserManagement.setOnClickListener {
            startActivity(Intent(this, UserManagementActivity::class.java))
        }

        // إدارة السيرفرات
        binding.cardServerManagement.setOnClickListener {
            startActivity(Intent(this, ServerAdminActivity::class.java))
        }

        // إدارة الاشتراكات
        binding.cardSubscriptionManagement?.setOnClickListener {
            startActivity(Intent(this, SubscriptionManagementActivity::class.java))
        }

        // تخصيص السيرفرات
        binding.cardAssignServers?.setOnClickListener {
            startActivity(Intent(this, AssignServersActivity::class.java))
        }

        // إعدادات المدير
        binding.cardAdminSettings?.setOnClickListener {
            startActivity(Intent(this, AdminSettingsActivity::class.java))
        }

        // الإحصائيات
        binding.cardStatistics?.setOnClickListener {
            startActivity(Intent(this, StatisticsActivity::class.java))
        }

        binding.fabAddUser?.setOnClickListener {
            startActivity(Intent(this, AddUserActivity::class.java))
        }

        binding.swipeRefresh.setOnRefreshListener {
            loadDashboardData()
        }
    }

    private fun checkAdminAccess() {
        lifecycleScope.launch(Dispatchers.IO) {
            val isAdmin = SuperAdminManager.canAccessAdminPanel()

            launch(Dispatchers.Main) {
                if (!isAdmin) {
                    toastError("ليس لديك صلاحية للوصول إلى لوحة التحكم")
                    startActivity(Intent(this@AdminPanelActivity, MainActivity::class.java))
                    finish()
                }
            }
        }
    }

    private fun loadDashboardData() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val stats = FirebaseUserManager.getAdminStats()
                
                launch(Dispatchers.Main) {
                    binding.swipeRefresh.isRefreshing = false
                    updateDashboardStats(stats)
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    binding.swipeRefresh.isRefreshing = false
                    toastError("خطأ في تحميل البيانات: ${e.message}")
                }
            }
        }
    }

    private fun updateDashboardStats(stats: FirebaseUserManager.AdminStatistics) {
        binding.apply {
            tvTotalUsers.text = stats.totalUsers.toString()
            tvActiveUsers.text = stats.activeUsers.toString()
            tvPremiumUsers.text = stats.premiumUsers.toString()
            tvTotalServers.text = stats.totalServers.toString()
        }
    }

    private fun loadUsers() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val users = FirebaseUserManager.getAllUsers()
                
                launch(Dispatchers.Main) {
                    usersAdapter.updateUsers(users)
                    
                    if (users.isEmpty()) {
                        binding.layoutEmptyUsers.visibility = android.view.View.VISIBLE
                        binding.recyclerViewUsers.visibility = android.view.View.GONE
                    } else {
                        binding.layoutEmptyUsers.visibility = android.view.View.GONE
                        binding.recyclerViewUsers.visibility = android.view.View.VISIBLE
                    }
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    toastError("خطأ في تحميل المستخدمين: ${e.message}")
                }
            }
        }
    }

    override fun editUser(user: FirebaseUserModel) {
        val intent = Intent(this, EditUserActivity::class.java).apply {
            putExtra("user_id", user.uid)
        }
        startActivity(intent)
    }

    override fun toggleUserStatus(user: FirebaseUserModel) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val updatedUser = user.copy(isActive = !user.isActive)
                val success = FirebaseUserManager.updateUser(updatedUser)
                
                launch(Dispatchers.Main) {
                    if (success) {
                        toastSuccess("تم تحديث حالة المستخدم")
                        loadUsers()
                    } else {
                        toastError("فشل في تحديث حالة المستخدم")
                    }
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    toastError("خطأ في تحديث المستخدم: ${e.message}")
                }
            }
        }
    }

    override fun assignServersToUser(user: FirebaseUserModel) {
        val intent = Intent(this, AssignServersActivity::class.java).apply {
            putExtra("user_id", user.uid)
            putExtra("user_name", user.displayName ?: user.username)
            putExtra("user_email", user.email)
        }
        startActivity(intent)
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_admin_panel, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_logout -> {
                performLogout()
                true
            }
            R.id.action_settings -> {
                startActivity(Intent(this, AdminSettingsActivity::class.java))
                true
            }
            R.id.action_backup -> {
                toast("نسخ احتياطي قريباً")
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun performLogout() {
        lifecycleScope.launch(Dispatchers.IO) {
            val success = FirebaseAuthManager.signOut()
            
            launch(Dispatchers.Main) {
                if (success) {
                    startActivity(Intent(this@AdminPanelActivity, LoginActivity::class.java).apply {
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    })
                    finish()
                } else {
                    toastError("فشل في تسجيل الخروج")
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        loadDashboardData()
    }
}
