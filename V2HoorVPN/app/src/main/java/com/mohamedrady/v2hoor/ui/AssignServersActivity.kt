package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.SearchView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityAssignServersBinding
import com.mohamedrady.v2hoor.firebase.FirebaseServerManager
import com.mohamedrady.v2hoor.firebase.FirebaseUserManager
import com.mohamedrady.v2hoor.firebase.FirebaseUserModel
import com.mohamedrady.v2hoor.firebase.FirebaseServerModel
import com.mohamedrady.v2hoor.ui.adapter.AssignServerAdapter
import com.mohamedrady.v2hoor.util.toast
import com.mohamedrady.v2hoor.util.toastError
import com.mohamedrady.v2hoor.util.toastSuccess
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Assign Servers Activity - تخصيص السيرفرات للمستخدمين
 */
class AssignServersActivity : BaseActivity() {

    private lateinit var binding: ActivityAssignServersBinding
    private lateinit var adapter: AssignServerAdapter
    private var currentUser: FirebaseUserModel? = null
    private var userId: String? = null
    private var allServers = mutableListOf<FirebaseServerModel>()
    private var assignedServerIds = mutableSetOf<String>()
    private var isLoading = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAssignServersBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Get user data from intent
        userId = intent.getStringExtra("user_id")
        val userName = intent.getStringExtra("user_name")
        val userEmail = intent.getStringExtra("user_email")

        // Validate required data
        if (userId.isNullOrEmpty()) {
            toastError("خطأ: معرف المستخدم مفقود")
            finish()
            return
        }

        setupToolbar()
        setupRecyclerView()
        setupClickListeners()

        // Set initial user info if available
        if (!userName.isNullOrEmpty()) {
            supportActionBar?.subtitle = userName
        } else if (!userEmail.isNullOrEmpty()) {
            supportActionBar?.subtitle = userEmail
        }

        loadData()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.assign_servers_title)
        }
    }

    private fun setupRecyclerView() {
        adapter = AssignServerAdapter(
            servers = allServers,
            assignedServerIds = assignedServerIds,
            onServerToggle = { server, isAssigned ->
                toggleServerAssignment(server, isAssigned)
            }
        )

        binding.recyclerViewServers.apply {
            layoutManager = LinearLayoutManager(this@AssignServersActivity)
            adapter = <EMAIL>
        }
    }

    private fun setupClickListeners() {
        binding.btnSaveAssignments.setOnClickListener {
            saveAssignments()
        }

        binding.btnSelectAll.setOnClickListener {
            selectAllServers()
        }

        binding.btnDeselectAll.setOnClickListener {
            deselectAllServers()
        }

        binding.swipeRefresh.setOnRefreshListener {
            loadData()
        }
    }

    private fun loadData() {
        if (userId.isNullOrEmpty()) {
            toastError("خطأ: معرف المستخدم مفقود")
            finish()
            return
        }

        setLoading(true)

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Load user data
                val user = FirebaseUserManager.getUserById(userId!!)

                if (user == null) {
                    withContext(Dispatchers.Main) {
                        setLoading(false)
                        toastError("خطأ: لم يتم العثور على المستخدم")
                        finish()
                    }
                    return@launch
                }

                // Load all servers
                val servers = FirebaseServerManager.getAllServers()

                // Load assigned servers for this user
                val userServers = FirebaseServerManager.getUserServers(userId!!)
                val assignedIds = userServers.map { it.id }.toSet()

                withContext(Dispatchers.Main) {
                    setLoading(false)

                    currentUser = user
                    allServers.clear()
                    allServers.addAll(servers)
                    assignedServerIds.clear()
                    assignedServerIds.addAll(assignedIds)

                    // Update UI with user information
                    updateUserInfo(user)
                    updateServerCount()
                    updateUI()
                    adapter.notifyDataSetChanged()
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    setLoading(false)
                    toastError(getString(R.string.error_loading_data, e.message))
                }
            }
        }
    }

    private fun updateUI() {
        // This method is now handled by updateUserInfo and updateServerCount
    }

    private fun toggleServerAssignment(server: FirebaseServerModel, isAssigned: Boolean) {
        val maxServers = currentUser?.subscription?.maxServers ?: 0

        if (isAssigned) {
            // Check if user can assign more servers
            if (maxServers > 0 && assignedServerIds.size >= maxServers) {
                toastError(getString(R.string.server_limit_reached, maxServers))
                return
            }
            assignedServerIds.add(server.id)
        } else {
            assignedServerIds.remove(server.id)
        }

        updateUI()
        adapter.notifyDataSetChanged()
    }

    private fun selectAllServers() {
        val maxServers = currentUser?.subscription?.maxServers ?: 0

        if (maxServers > 0 && allServers.size > maxServers) {
            // Select only up to the limit
            assignedServerIds.clear()
            assignedServerIds.addAll(allServers.take(maxServers).map { it.id })
            toastError(getString(R.string.server_limit_reached, maxServers))
        } else {
            // Select all servers
            assignedServerIds.clear()
            assignedServerIds.addAll(allServers.map { it.id })
        }

        updateUI()
        adapter.notifyDataSetChanged()
    }

    private fun deselectAllServers() {
        assignedServerIds.clear()
        updateUI()
        adapter.notifyDataSetChanged()
    }

    private fun saveAssignments() {
        if (currentUser == null) return

        setLoading(true)

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Save server assignments to Firebase
                val success = FirebaseServerManager.assignServersToUser(
                    userId = userId!!,
                    serverIds = assignedServerIds.toList()
                )

                withContext(Dispatchers.Main) {
                    setLoading(false)

                    if (success) {
                        toastSuccess(getString(R.string.server_assignments_saved))
                        finish()
                    } else {
                        toastError(getString(R.string.error_saving_assignments))
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    setLoading(false)
                    toastError(getString(R.string.error_saving_assignments, e.message))
                }
            }
        }
    }

    private fun setLoading(loading: Boolean) {
        isLoading = loading
        binding.swipeRefresh.isRefreshing = loading
        binding.btnSaveAssignments.isEnabled = !loading
        binding.btnSelectAll.isEnabled = !loading
        binding.btnDeselectAll.isEnabled = !loading
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_assign_servers, menu)

        val searchItem = menu?.findItem(R.id.action_search)
        val searchView = searchItem?.actionView as? SearchView

        searchView?.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                adapter.filter(newText ?: "")
                return true
            }
        })

        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_refresh -> {
                loadData()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun updateUserInfo(user: FirebaseUserModel) {
        val userInfo = buildString {
            append(user.displayName ?: user.username ?: "مستخدم غير معروف")
            if (!user.email.isNullOrEmpty()) {
                append(" - ${user.email}")
            }
            user.subscription?.let { subscription ->
                append(" - ${subscription.planName ?: "خطة أساسية"}")
            }
        }
        binding.tvUserInfo.text = userInfo

        // Update toolbar subtitle
        supportActionBar?.subtitle = user.displayName ?: user.username ?: user.email
    }

    private fun updateServerCount() {
        val assignedCount = assignedServerIds.size
        val totalServers = allServers.size
        val maxServers = currentUser?.subscription?.maxServers ?: -1

        val countText = if (maxServers > 0) {
            "السيرفرات المخصصة: $assignedCount من $maxServers (إجمالي: $totalServers)"
        } else {
            "السيرفرات المخصصة: $assignedCount (إجمالي: $totalServers)"
        }

        binding.tvServerCount.text = countText

        // Show warning if limit reached
        if (maxServers > 0 && assignedCount >= maxServers) {
            binding.tvLimitWarning.visibility = android.view.View.VISIBLE
            binding.tvLimitWarning.text = "تم الوصول للحد الأقصى من السيرفرات ($maxServers)"
        } else {
            binding.tvLimitWarning.visibility = android.view.View.GONE
        }
    }
}
