package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityStatisticsBinding
import com.mohamedrady.v2hoor.firebase.FirebaseUserManager
import com.mohamedrady.v2hoor.firebase.FirebaseServerManager
import com.mohamedrady.v2hoor.util.toastError
import com.mohamedrady.v2hoor.utils.SuperAdminManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.NumberFormat
import java.util.*

/**
 * Statistics Activity - صفحة الإحصائيات الشاملة
 */
class StatisticsActivity : BaseActivity() {

    private lateinit var binding: ActivityStatisticsBinding
    private var isLoading = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityStatisticsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupSwipeRefresh()
        checkPermissions()
        loadStatistics()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = "إحصائيات النظام"
        }
    }

    private fun setupSwipeRefresh() {
        binding.swipeRefresh.setOnRefreshListener {
            loadStatistics()
        }
    }

    private fun checkPermissions() {
        lifecycleScope.launch(Dispatchers.IO) {
            val canView = SuperAdminManager.canViewAllStatistics()
            
            withContext(Dispatchers.Main) {
                if (!canView) {
                    toastError("ليس لديك صلاحية لعرض الإحصائيات")
                    finish()
                }
            }
        }
    }

    private fun loadStatistics() {
        setLoading(true)

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Load user statistics
                val allUsers = FirebaseUserManager.getAllUsers()
                val activeUsers = allUsers.filter { it.isActive }
                val adminUsers = allUsers.filter { it.isAdmin || it.role == "admin" || it.role == "super_admin" }
                val premiumUsers = allUsers.filter { 
                    it.subscription?.isActive == true && it.subscription?.planType != "free" 
                }

                // Load server statistics
                val allServers = FirebaseServerManager.getAllServers()
                val activeServers = allServers.filter { it.isActive }

                // Calculate data usage (mock data for now)
                val totalDataUsage = allUsers.sumOf { 
                    (it.dataUsage?.totalUpload ?: 0L) + (it.dataUsage?.totalDownload ?: 0L) 
                }

                withContext(Dispatchers.Main) {
                    setLoading(false)
                    updateUI(
                        totalUsers = allUsers.size,
                        activeUsers = activeUsers.size,
                        adminUsers = adminUsers.size,
                        premiumUsers = premiumUsers.size,
                        totalServers = allServers.size,
                        activeServers = activeServers.size,
                        totalDataUsage = totalDataUsage
                    )
                }

            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    setLoading(false)
                    toastError("خطأ في تحميل الإحصائيات: ${e.message}")
                }
            }
        }
    }

    private fun updateUI(
        totalUsers: Int,
        activeUsers: Int,
        adminUsers: Int,
        premiumUsers: Int,
        totalServers: Int,
        activeServers: Int,
        totalDataUsage: Long
    ) {
        // User Statistics
        binding.tvTotalUsers.text = NumberFormat.getInstance().format(totalUsers)
        binding.tvActiveUsers.text = NumberFormat.getInstance().format(activeUsers)
        binding.tvAdminUsers.text = NumberFormat.getInstance().format(adminUsers)
        binding.tvPremiumUsers.text = NumberFormat.getInstance().format(premiumUsers)

        // Server Statistics
        binding.tvTotalServers.text = NumberFormat.getInstance().format(totalServers)
        binding.tvActiveServers.text = NumberFormat.getInstance().format(activeServers)

        // Data Usage
        binding.tvTotalDataUsage.text = formatBytes(totalDataUsage)

        // Calculate percentages
        val activeUserPercentage = if (totalUsers > 0) (activeUsers * 100) / totalUsers else 0
        val activeServerPercentage = if (totalServers > 0) (activeServers * 100) / totalServers else 0

        binding.tvActiveUserPercentage.text = "$activeUserPercentage%"
        binding.tvActiveServerPercentage.text = "$activeServerPercentage%"

        // Update progress bars
        binding.progressActiveUsers.progress = activeUserPercentage
        binding.progressActiveServers.progress = activeServerPercentage

        // Show/hide empty state
        binding.layoutStatistics.visibility = View.VISIBLE
        binding.layoutEmpty.visibility = View.GONE
    }

    private fun formatBytes(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        var size = bytes.toDouble()
        var unitIndex = 0

        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }

        return String.format(Locale.getDefault(), "%.2f %s", size, units[unitIndex])
    }

    private fun setLoading(loading: Boolean) {
        isLoading = loading
        binding.swipeRefresh.isRefreshing = loading
        
        if (loading) {
            binding.layoutStatistics.visibility = View.GONE
            binding.layoutEmpty.visibility = View.GONE
            binding.progressBar.visibility = View.VISIBLE
        } else {
            binding.progressBar.visibility = View.GONE
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_statistics, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_refresh -> {
                loadStatistics()
                true
            }
            R.id.action_export -> {
                exportStatistics()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun exportStatistics() {
        // TODO: Implement statistics export functionality
        toastError("ميزة التصدير قيد التطوير")
    }
}
