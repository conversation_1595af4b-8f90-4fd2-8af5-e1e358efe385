package com.mohamedrady.v2hoor.firebase

import android.util.Log
import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseError
import com.google.firebase.database.DatabaseReference
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.database.ValueEventListener
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await

/**
 * Firebase User Manager for user operations
 */
object FirebaseUserManager {
    private const val TAG = "FirebaseUserManager"
    private const val USERS_PATH = "users"
    
    private val database: DatabaseReference by lazy {
        FirebaseDatabase.getInstance("https://mrelfeky-209615-default-rtdb.firebaseio.com/").reference
    }
    
    private val usersRef: DatabaseReference by lazy {
        database.child(USERS_PATH)
    }
    
    /**
     * Initialize default admin user
     */
    suspend fun initializeDefaultAdmin(): Boolean {
        return try {
            val adminUser = FirebaseUserModel.createDefaultAdmin()
            val snapshot = usersRef.child(adminUser.uid).get().await()
            
            if (!snapshot.exists()) {
                usersRef.child(adminUser.uid).setValue(adminUser).await()
                Log.d(TAG, "Default admin user created")
            } else {
                Log.d(TAG, "Default admin user already exists")
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize default admin", e)
            false
        }
    }
    
    /**
     * Get user by ID
     */
    suspend fun getUserById(userId: String): FirebaseUserModel? {
        return try {
            val snapshot = usersRef.child(userId).get().await()
            snapshot.getValue(FirebaseUserModel::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user by ID: $userId", e)
            null
        }
    }
    
    /**
     * Get all users
     */
    suspend fun getAllUsers(): List<FirebaseUserModel> {
        return try {
            val snapshot = usersRef.get().await()
            val users = mutableListOf<FirebaseUserModel>()
            
            snapshot.children.forEach { child ->
                child.getValue(FirebaseUserModel::class.java)?.let { user ->
                    users.add(user)
                }
            }
            
            Log.d(TAG, "Retrieved ${users.size} users from Firebase")
            users.sortedBy { it.createdAt }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting users from Firebase", e)
            emptyList()
        }
    }
    
    /**
     * Get active users only
     */
    suspend fun getActiveUsers(): List<FirebaseUserModel> {
        return try {
            val snapshot = usersRef.orderByChild("isActive").equalTo(true).get().await()
            val users = mutableListOf<FirebaseUserModel>()
            
            snapshot.children.forEach { child ->
                child.getValue(FirebaseUserModel::class.java)?.let { user ->
                    users.add(user)
                }
            }
            
            users.sortedBy { it.createdAt }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting active users from Firebase", e)
            emptyList()
        }
    }
    
    /**
     * Add user to Firebase
     */
    suspend fun addUser(user: FirebaseUserModel): Boolean {
        return try {
            val userId = if (user.uid.isEmpty()) {
                usersRef.push().key ?: return false
            } else {
                user.uid
            }
            
            val userWithId = user.copy(
                uid = userId,
                createdAt = if (user.createdAt == 0L) System.currentTimeMillis() else user.createdAt,
                updatedAt = System.currentTimeMillis()
            )
            
            usersRef.child(userId).setValue(userWithId).await()
            Log.d(TAG, "User added to Firebase: $userId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error adding user to Firebase", e)
            false
        }
    }
    
    /**
     * Update user in Firebase
     */
    suspend fun updateUser(user: FirebaseUserModel): Boolean {
        return try {
            if (user.uid.isEmpty()) return false
            
            val updatedUser = user.copy(updatedAt = System.currentTimeMillis())
            usersRef.child(user.uid).setValue(updatedUser).await()
            Log.d(TAG, "User updated in Firebase: ${user.uid}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating user in Firebase", e)
            false
        }
    }
    
    /**
     * Update user subscription
     */
    suspend fun updateUserSubscription(userId: String, subscription: UserSubscription): Boolean {
        return try {
            val updates = mapOf(
                "subscription" to subscription,
                "updatedAt" to System.currentTimeMillis()
            )
            usersRef.child(userId).updateChildren(updates).await()
            Log.d(TAG, "User subscription updated: $userId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating user subscription", e)
            false
        }
    }
    
    /**
     * Assign servers to user
     */
    suspend fun assignServersToUser(userId: String, serverIds: List<String>): Boolean {
        return try {
            val updates = mapOf(
                "assignedServers" to serverIds,
                "updatedAt" to System.currentTimeMillis()
            )
            usersRef.child(userId).updateChildren(updates).await()
            
            // Also update server assignments in FirebaseServerManager
            FirebaseServerManager.assignServersToUser(userId, serverIds)
            
            Log.d(TAG, "Servers assigned to user: $userId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error assigning servers to user", e)
            false
        }
    }
    
    /**
     * Update user data usage
     */
    suspend fun updateUserDataUsage(userId: String, dataUsage: UserDataUsage): Boolean {
        return try {
            val updates = mapOf(
                "dataUsage" to dataUsage,
                "subscription.dataUsed" to dataUsage.getTotalUsage(),
                "updatedAt" to System.currentTimeMillis()
            )
            usersRef.child(userId).updateChildren(updates).await()
            Log.d(TAG, "User data usage updated: $userId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating user data usage", e)
            false
        }
    }
    
    /**
     * Deactivate user
     */
    suspend fun deactivateUser(userId: String): Boolean {
        return try {
            val updates = mapOf(
                "isActive" to false,
                "updatedAt" to System.currentTimeMillis()
            )
            usersRef.child(userId).updateChildren(updates).await()
            Log.d(TAG, "User deactivated: $userId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error deactivating user", e)
            false
        }
    }
    
    /**
     * Activate user
     */
    suspend fun activateUser(userId: String): Boolean {
        return try {
            val updates = mapOf(
                "isActive" to true,
                "updatedAt" to System.currentTimeMillis()
            )
            usersRef.child(userId).updateChildren(updates).await()
            Log.d(TAG, "User activated: $userId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error activating user", e)
            false
        }
    }
    
    /**
     * Delete user from Firebase
     */
    suspend fun deleteUser(userId: String): Boolean {
        return try {
            usersRef.child(userId).removeValue().await()
            Log.d(TAG, "User deleted from Firebase: $userId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting user from Firebase", e)
            false
        }
    }
    
    /**
     * Listen to user changes in real-time
     */
    fun listenToUserChanges(): Flow<List<FirebaseUserModel>> = callbackFlow {
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val users = mutableListOf<FirebaseUserModel>()
                
                snapshot.children.forEach { child ->
                    child.getValue(FirebaseUserModel::class.java)?.let { user ->
                        users.add(user)
                    }
                }
                
                trySend(users.sortedBy { it.createdAt })
            }

            override fun onCancelled(error: DatabaseError) {
                Log.e(TAG, "Error listening to user changes", error.toException())
                close(error.toException())
            }
        }
        
        usersRef.addValueEventListener(listener)
        
        awaitClose {
            usersRef.removeEventListener(listener)
        }
    }
    
    /**
     * Get users by subscription type
     */
    suspend fun getUsersBySubscriptionType(planType: String): List<FirebaseUserModel> {
        return try {
            val snapshot = usersRef.orderByChild("subscription/planType").equalTo(planType).get().await()
            val users = mutableListOf<FirebaseUserModel>()

            snapshot.children.forEach { child ->
                child.getValue(FirebaseUserModel::class.java)?.let { user ->
                    users.add(user)
                }
            }

            users.sortedBy { it.createdAt }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting users by subscription type", e)
            emptyList()
        }
    }

    /**
     * Create user with email and password (alias for compatibility)
     */
    suspend fun createUserWithEmailAndPassword(email: String, password: String): FirebaseAuthManager.AuthResult {
        return FirebaseAuthManager.createUserWithEmailAndPassword(email, password, "")
    }

    /**
     * Send password reset email (alias for compatibility)
     */
    suspend fun sendPasswordResetEmail(email: String): Boolean {
        return FirebaseAuthManager.sendPasswordResetEmail(email)
    }

    /**
     * Get admin statistics
     */
    suspend fun getAdminStats(): AdminStatistics {
        return try {
            val allUsers = getAllUsers()
            val totalUsers = allUsers.size
            val activeUsers = allUsers.count { it.isActive }
            val premiumUsers = allUsers.count { it.subscription.planType != "free" }

            AdminStatistics(
                totalUsers = totalUsers,
                activeUsers = activeUsers,
                premiumUsers = premiumUsers,
                totalServers = FirebaseServerManager.getAllServers().size
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting admin stats", e)
            AdminStatistics()
        }
    }

    /**
     * Get admin statistics (alias for compatibility)
     */
    suspend fun getAdminStatistics(): AdminStatistics {
        return getAdminStats()
    }

    /**
     * Admin Statistics data class
     */
    data class AdminStatistics(
        val totalUsers: Int = 0,
        val activeUsers: Int = 0,
        val premiumUsers: Int = 0,
        val totalServers: Int = 0
    )
}
