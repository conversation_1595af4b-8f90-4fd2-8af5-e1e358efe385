package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import androidx.activity.OnBackPressedCallback
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityLoginBinding
import com.mohamedrady.v2hoor.firebase.FirebaseAuthManager
import com.mohamedrady.v2hoor.firebase.FirebaseUserModel
import com.mohamedrady.v2hoor.firebase.FirebaseUserManager
import com.mohamedrady.v2hoor.firebase.FirebaseSubscriptionModel
import com.mohamedrady.v2hoor.handler.MmkvManager
import com.mohamedrady.v2hoor.utils.SuperAdminManager
import android.util.Log
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.mohamedrady.v2hoor.util.*
import com.mohamedrady.v2hoor.BuildConfig

/**
 * Login Activity for V2Hoor VPN
 */
class LoginActivity : BaseActivity() {
    private val binding by lazy { ActivityLoginBinding.inflate(layoutInflater) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        // Handle back press
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finishAffinity()
            }
        })

        setupUI()
        setupListeners()
        checkAutoLogin()
    }

    private fun setupUI() {
        // Hide action bar
        supportActionBar?.hide()
        
        // Set default values for testing
        if (BuildConfig.DEBUG) {
            binding.etEmail.setText("<EMAIL>")
            binding.etPassword.setText("123456")
        }
    }

    private fun setupListeners() {
        binding.btnLogin.setOnClickListener {
            performLogin()
        }
        
        binding.btnRegister.setOnClickListener {
            startActivity(Intent(this, RegisterActivity::class.java))
        }
        
        binding.tvForgotPassword.setOnClickListener {
            toast("إعادة تعيين كلمة المرور قريباً")
        }
        
        binding.btnGuestMode.setOnClickListener {
            enterGuestMode()
        }
    }

    private fun checkAutoLogin() {
        if (FirebaseAuthManager.isLoggedIn()) {
            val rememberMe = MmkvManager.decodeBool("remember_me", false)
            if (rememberMe) {
                navigateToMain()
            }
        }
    }

    private fun performLogin() {
        val email = binding.etEmail.text.toString().trim()
        val password = binding.etPassword.text.toString().trim()
        
        if (!validateInput(email, password)) return
        
        showLoading(true)
        
        lifecycleScope.launch(Dispatchers.IO) {
            when (val result = FirebaseAuthManager.signIn(email, password)) {
                is FirebaseAuthManager.AuthResult.Success -> {
                    launch(Dispatchers.Main) {
                        showLoading(false)
                        handleLoginSuccess(result.user)
                    }
                }
                is FirebaseAuthManager.AuthResult.Error -> {
                    launch(Dispatchers.Main) {
                        showLoading(false)
                        toastError(result.message)
                    }
                }
            }
        }
    }

    private fun validateInput(email: String, password: String): Boolean {
        if (email.isEmpty()) {
            binding.etEmail.error = "يرجى إدخال البريد الإلكتروني"
            binding.etEmail.requestFocus()
            return false
        }
        
        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            binding.etEmail.error = "البريد الإلكتروني غير صحيح"
            binding.etEmail.requestFocus()
            return false
        }
        
        if (password.isEmpty()) {
            binding.etPassword.error = "يرجى إدخال كلمة المرور"
            binding.etPassword.requestFocus()
            return false
        }
        
        if (password.length < 6) {
            binding.etPassword.error = "كلمة المرور يجب أن تكون 6 أحرف أو أكثر"
            binding.etPassword.requestFocus()
            return false
        }
        
        return true
    }

    private fun handleLoginSuccess(user: com.google.firebase.auth.FirebaseUser) {
        // Save login state
        val rememberMe = binding.cbRememberMe.isChecked
        MmkvManager.encode("remember_me", rememberMe)
        MmkvManager.encode("current_user_id", user.uid)
        MmkvManager.encode("last_login_check", System.currentTimeMillis())

        val displayName = user.displayName ?: user.email?.substringBefore("@") ?: "مستخدم"
        toastSuccess("مرحباً $displayName")

        // Check if user is super admin and handle profile setup
        val isSuperAdmin = SuperAdminManager.isSuperAdmin(user)

        if (isSuperAdmin) {
            // Create or update super admin profile
            lifecycleScope.launch(Dispatchers.IO) {
                val success = SuperAdminManager.ensureSuperAdminProfile(user)
                withContext(Dispatchers.Main) {
                    if (success) {
                        Log.d("LoginActivity", "Super admin profile ensured successfully")
                        toast("مرحباً مدير النظام - تم تحديث الملف الشخصي")
                    } else {
                        Log.w("LoginActivity", "Failed to ensure super admin profile")
                    }
                }
            }
        }

        val userRole = SuperAdminManager.getUserRole()
        MmkvManager.encode("user_role", userRole)

        // Navigate based on user role
        if (isSuperAdmin) {
            navigateToAdminPanel()
        } else {
            navigateToMain()
        }
    }

    private fun createUserProfile(firebaseUser: com.google.firebase.auth.FirebaseUser) {
        showLoading(true)

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Create user profile in database
                val userData = FirebaseUserModel(
                    uid = firebaseUser.uid,
                    email = firebaseUser.email ?: "",
                    username = firebaseUser.email?.substringBefore("@") ?: "",
                    displayName = firebaseUser.displayName ?: firebaseUser.email?.substringBefore("@") ?: "مستخدم",
                    role = FirebaseUserModel.ROLE_USER,
                    subscription = createFreeSubscription()
                )

                val success = FirebaseUserManager.updateUser(userData)

                launch(Dispatchers.Main) {
                    showLoading(false)

                    if (success) {
                        // Save login state
                        val rememberMe = binding.cbRememberMe.isChecked
                        MmkvManager.encode("remember_me", rememberMe)
                        MmkvManager.encode("current_user_id", userData.uid)
                        MmkvManager.encode("user_role", userData.role)
                        MmkvManager.encode("last_login_check", System.currentTimeMillis())

                        toastSuccess("مرحباً ${userData.displayName}")
                        navigateToMain()
                    } else {
                        toastError("فشل في إنشاء ملف المستخدم")
                    }
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    showLoading(false)
                    toastError("خطأ في إنشاء الملف: ${e.message}")
                }
            }
        }
    }

    private fun createFreeSubscription(): FirebaseSubscriptionModel {
        return FirebaseSubscriptionModel(
            id = java.util.UUID.randomUUID().toString(),
            planId = "free",
            planName = "خطة مجانية",
            planType = "free",
            isSubscriptionActive = true,
            maxServers = 3,
            maxDevices = 1,
            bandwidthLimit = 1024, // 1GB
            speedLimit = 10 // 10 Mbps
        )
    }

    private fun enterGuestMode() {
        MmkvManager.encode("guest_mode", true)
        MmkvManager.remove("current_user_id")
        MmkvManager.remove("user_role")
        
        toast("دخول كضيف - وصول محدود للسيرفرات")
        navigateToMain()
    }

    private fun navigateToMain() {
        startActivity(Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        })
        finish()
    }

    private fun navigateToAdminPanel() {
        startActivity(Intent(this, AdminPanelActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        })
        finish()
    }



    private fun showLoading(show: Boolean) {
        binding.progressBar.visibility = if (show) View.VISIBLE else View.GONE
        binding.btnLogin.isEnabled = !show
        binding.btnRegister.isEnabled = !show
        binding.btnGuestMode.isEnabled = !show
    }


}
