package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityUserManagementBinding
import com.mohamedrady.v2hoor.firebase.FirebaseUserManager
import com.mohamedrady.v2hoor.firebase.FirebaseUserModel
import com.mohamedrady.v2hoor.ui.adapter.AdminUsersAdapter
import com.mohamedrady.v2hoor.ui.adapter.UserActionListener
import com.mohamedrady.v2hoor.util.toast
import com.mohamedrady.v2hoor.util.toastError
import com.mohamedrady.v2hoor.util.toastSuccess
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Activity for managing users
 */
class UserManagementActivity : BaseActivity(), UserActionListener {
    
    private lateinit var binding: ActivityUserManagementBinding
    private lateinit var adapter: AdminUsersAdapter
    private val users = mutableListOf<FirebaseUserModel>()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityUserManagementBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupRecyclerView()
        setupFab()
        loadUsers()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = "إدارة المستخدمين"
        }
    }
    
    private fun setupRecyclerView() {
        adapter = AdminUsersAdapter(this, this)
        binding.recyclerViewUsers.apply {
            layoutManager = LinearLayoutManager(this@UserManagementActivity)
            adapter = <EMAIL>
        }
    }
    
    private fun setupFab() {
        binding.fabAddUser.setOnClickListener {
            startActivity(Intent(this, AddUserActivity::class.java))
        }
    }
    
    override fun onResume() {
        super.onResume()
        loadUsers()
    }
    
    private fun loadUsers() {
        binding.progressBar.visibility = View.VISIBLE
        binding.textViewEmpty.visibility = View.GONE
        
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val usersList = FirebaseUserManager.getAllUsers()
                
                withContext(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    
                    if (usersList.isNotEmpty()) {
                        users.clear()
                        users.addAll(usersList)
                        adapter.notifyDataSetChanged()
                        binding.textViewEmpty.visibility = View.GONE
                    } else {
                        binding.textViewEmpty.visibility = View.VISIBLE
                        binding.textViewEmpty.text = "لا توجد مستخدمين"
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    binding.textViewEmpty.visibility = View.VISIBLE
                    binding.textViewEmpty.text = "خطأ في تحميل المستخدمين"
                    toastError("خطأ في تحميل المستخدمين: ${e.message}")
                }
            }
        }
    }
    
    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_user_management, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_refresh -> {
                loadUsers()
                true
            }
            R.id.action_add_user -> {
                startActivity(Intent(this, AddUserActivity::class.java))
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    // Methods called by AdminUsersAdapter
    override fun editUser(user: FirebaseUserModel) {
        val intent = Intent(this, EditUserActivity::class.java).apply {
            putExtra("user_id", user.uid)
            putExtra("user_email", user.email)
            putExtra("user_name", user.displayName)
        }
        startActivity(intent)
    }
    
    override fun toggleUserStatus(user: FirebaseUserModel) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val updatedUser = user.copy(isActive = !user.isActive)
                val success = FirebaseUserManager.updateUser(updatedUser)
                
                withContext(Dispatchers.Main) {
                    if (success) {
                        toastSuccess("تم تحديث حالة المستخدم")
                        loadUsers()
                    } else {
                        toastError("فشل في تحديث حالة المستخدم")
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    toastError("خطأ في تحديث المستخدم: ${e.message}")
                }
            }
        }
    }
    
    override fun assignServersToUser(user: FirebaseUserModel) {
        val intent = Intent(this, AssignServersActivity::class.java).apply {
            putExtra("user_id", user.uid)
            putExtra("user_name", user.displayName)
            putExtra("user_email", user.email)
        }
        startActivity(intent)
    }
    
    fun deleteUser(user: FirebaseUserModel) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("حذف المستخدم")
            .setMessage("هل أنت متأكد من حذف المستخدم ${user.displayName}؟")
            .setPositiveButton("حذف") { _, _ ->
                performDeleteUser(user)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
    
    private fun performDeleteUser(user: FirebaseUserModel) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val success = FirebaseUserManager.deleteUser(user.uid)
                
                withContext(Dispatchers.Main) {
                    if (success) {
                        toastSuccess("تم حذف المستخدم")
                        loadUsers()
                    } else {
                        toastError("فشل في حذف المستخدم")
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    toastError("خطأ في حذف المستخدم: ${e.message}")
                }
            }
        }
    }
}
