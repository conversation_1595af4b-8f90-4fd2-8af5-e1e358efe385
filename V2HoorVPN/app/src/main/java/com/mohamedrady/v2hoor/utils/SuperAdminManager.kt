package com.mohamedrady.v2hoor.utils

import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.mohamedrady.v2hoor.firebase.FirebaseAuthManager
import com.mohamedrady.v2hoor.firebase.FirebaseUserManager
import com.mohamedrady.v2hoor.firebase.FirebaseUserModel
import com.mohamedrady.v2hoor.firebase.FirebaseSubscriptionModel
import com.mohamedrady.v2hoor.handler.MmkvManager

/**
 * Super Admin Manager
 * Handles super admin <NAME_EMAIL>
 */
object SuperAdminManager {
    
    private const val TAG = "SuperAdminManager"
    private const val SUPER_ADMIN_EMAIL = "<EMAIL>"
    
    /**
     * Check if current user is super admin
     */
    fun isSuperAdmin(): Boolean {
        return try {
            val currentUser = FirebaseAuth.getInstance().currentUser
            currentUser?.email == SUPER_ADMIN_EMAIL
        } catch (e: Exception) {
            Log.e(TAG, "Error checking super admin status", e)
            false
        }
    }
    
    /**
     * Check if user is super admin by email
     */
    fun isSuperAdmin(email: String?): Boolean {
        return email == SUPER_ADMIN_EMAIL
    }
    
    /**
     * Check if user is super admin by FirebaseUser
     */
    fun isSuperAdmin(user: FirebaseUser?): Boolean {
        return user?.email == SUPER_ADMIN_EMAIL
    }
    
    /**
     * Check if user is super admin by FirebaseUserModel
     */
    fun isSuperAdmin(user: FirebaseUserModel?): Boolean {
        return user?.email == SUPER_ADMIN_EMAIL
    }
    
    /**
     * Check if current user is admin (super admin or regular admin)
     */
    suspend fun isAdmin(): Boolean {
        return try {
            // First check if super admin
            if (isSuperAdmin()) {
                return true
            }
            
            // Then check regular admin status
            FirebaseAuthManager.isCurrentUserAdmin()
        } catch (e: Exception) {
            Log.e(TAG, "Error checking admin status", e)
            isSuperAdmin() // Fallback to super admin check
        }
    }
    
    /**
     * Check if user has admin privileges (super admin or regular admin)
     */
    suspend fun hasAdminPrivileges(email: String?): Boolean {
        return try {
            // First check if super admin
            if (isSuperAdmin(email)) {
                return true
            }
            
            // Then check regular admin status
            // Note: getUserByEmail method doesn't exist, so we'll use a simpler check
            false // For now, only super admin has privileges
        } catch (e: Exception) {
            Log.e(TAG, "Error checking admin privileges", e)
            isSuperAdmin(email) // Fallback to super admin check
        }
    }
    
    /**
     * Check if current user can access admin panel
     */
    suspend fun canAccessAdminPanel(): Boolean {
        return try {
            val isLoggedIn = FirebaseAuthManager.isLoggedIn()
            val isGuestMode = MmkvManager.decodeBool("guest_mode", false)
            
            // Super admin always has access
            if (isSuperAdmin()) {
                return isLoggedIn && !isGuestMode
            }
            
            // Regular admin check
            isLoggedIn && !isGuestMode && FirebaseAuthManager.isCurrentUserAdmin()
        } catch (e: Exception) {
            Log.e(TAG, "Error checking admin panel access", e)
            isSuperAdmin() // Fallback to super admin check
        }
    }
    
    /**
     * Check if current user can manage servers
     */
    suspend fun canManageServers(): Boolean {
        return isAdmin() // Super admin or regular admin
    }
    
    /**
     * Check if current user can manage users
     */
    suspend fun canManageUsers(): Boolean {
        return isAdmin() // Super admin or regular admin
    }
    
    /**
     * Check if current user can view all statistics
     */
    suspend fun canViewAllStatistics(): Boolean {
        return isAdmin() // Super admin or regular admin
    }
    
    /**
     * Check if current user can modify system settings
     */
    suspend fun canModifySystemSettings(): Boolean {
        return isSuperAdmin() // Only super admin
    }
    
    /**
     * Check if current user can delete users
     */
    suspend fun canDeleteUsers(): Boolean {
        return isAdmin() // Super admin or regular admin
    }
    
    /**
     * Check if current user can create admin users
     */
    suspend fun canCreateAdminUsers(): Boolean {
        return isSuperAdmin() // Only super admin
    }
    
    /**
     * Check if current user can access all servers
     */
    suspend fun canAccessAllServers(): Boolean {
        return isAdmin() // Super admin or regular admin
    }
    
    /**
     * Check if current user can bypass subscription limits
     */
    suspend fun canBypassSubscriptionLimits(): Boolean {
        return isSuperAdmin() // Only super admin
    }
    
    /**
     * Get user role display name
     */
    fun getUserRoleDisplayName(): String {
        return if (isSuperAdmin()) {
            "مدير عام"
        } else {
            "مستخدم"
        }
    }
    
    /**
     * Get user role for storage
     */
    fun getUserRole(): String {
        return if (isSuperAdmin()) {
            "super_admin"
        } else {
            "user"
        }
    }
    
    /**
     * Create super admin user model
     */
    fun createSuperAdminUserModel(): FirebaseUserModel {
        return FirebaseUserModel(
            uid = "super_admin_${System.currentTimeMillis()}",
            email = SUPER_ADMIN_EMAIL,
            username = "super_admin",
            displayName = "مدير عام",
            isActive = true,
            isAdmin = true,
            role = "super_admin",
            subscription = UserSubscription(
                id = "super_admin_subscription",
                planId = "super_admin",
                planType = "unlimited",
                planName = "خطة إدارية عليا",
                expiryDate = Long.MAX_VALUE,
                dataLimit = -1, // Unlimited
                isActive = true,
                maxServers = -1, // Unlimited
                maxDevices = -1, // Unlimited
                bandwidthLimit = -1, // Unlimited
                speedLimit = -1, // Unlimited
                isSubscriptionActive = true
            ),
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
    }
}
