package com.mohamedrady.v2hoor.firebase

import com.mohamedrady.v2hoor.dto.EConfigType
import com.mohamedrady.v2hoor.dto.ProfileItem
import com.mohamedrady.v2hoor.dto.ServerConfig

/**
 * Firebase Server Model for storing server configurations
 */
data class FirebaseServerModel(
    var id: String = "",
    var name: String = "",
    var description: String = "",
    var configType: String = "",
    var server: String = "",
    var serverPort: String = "",
    var category: String = "",
    var country: String = "",
    var city: String = "",
    var provider: String = "",
    var speed: String = "",
    var ping: Long = 0,
    var sortOrder: Int = 0,
    var isActive: Boolean = true,
    var isReadOnly: Boolean = false,
    var createdAt: Long = 0,
    var updatedAt: Long = 0,
    
    // VMESS/VLESS specific fields
    var userId: String = "",
    var alterId: Int = 0,
    var security: String = "",
    var network: String = "",
    var headerType: String = "",
    var requestHost: String = "",
    var path: String = "",
    var streamSecurity: String = "",
    var allowInsecure: Boolean = false,
    var sni: String = "",
    var fingerprint: String = "",
    var alpn: String = "",
    var publicKey: String = "",
    var shortId: String = "",
    var spiderX: String = "",
    var flow: String = "",
    
    // Shadowsocks specific fields
    var method: String = "",
    var password: String = "",
    
    // Trojan specific fields
    var trojanPassword: String = "",
    
    // HTTP/SOCKS specific fields
    var username: String = "",
    var httpPassword: String = "",
    
    // WireGuard specific fields
    var secretKey: String = "",
    var localAddress: String = "",
    var mtu: Int = 1420,
    var reserved: String = "",
    
    // Additional metadata
    var tags: List<String> = emptyList(),
    var notes: String = "",

    // Compatibility properties for UI
    var host: String = "",
    var insecure: Boolean = false,

    // Missing Firebase fields
    var vmessPath: String = "",
    var subid: String = "",
    var vmessAid: Any = 0, // Can be String or Long
    var group: String = "",
    var obfs: String = "",
    var configurationId: String = "",
    var vmessAlpn2: String = "",
    var vmessFp: String = "",
    var vmessType: String = "",

    // Additional missing fields from Firebase
    var quicKey: String = "",
    var authority: String = "",
    var portHopping: String = "",
    var quicSecurity: String = "",
    var pinSHA256: String = "",
    var bandwidthDown: String = "",
    var mode: String = "",
    var preSharedKey: String = "",
    var portHoppingInterval: String = "",
    var xhttpExtra: String = "",
    var serviceName: String = "",
    var fingerPrint: String = "", // Note: case sensitive, different from fingerprint
    var obfsPassword: String = "",
    var xhttpMode: String = "",
    var seed: String = "",
    var bandwidthUp: String = ""
) {
    companion object {
        /**
         * Convert ProfileItem to FirebaseServerModel
         */
        fun fromProfileItem(profileItem: ProfileItem): FirebaseServerModel {
            return FirebaseServerModel(
                name = profileItem.remarks,
                configType = profileItem.configType?.name ?: "",
                server = profileItem.server ?: "",
                serverPort = profileItem.serverPort ?: "",
                userId = profileItem.password ?: "", // For VMESS/VLESS, password is the user ID
                security = profileItem.method ?: "",
                network = profileItem.network ?: "",
                headerType = profileItem.headerType ?: "",
                requestHost = profileItem.host ?: "",
                path = profileItem.path ?: "",
                streamSecurity = profileItem.security ?: "",
                allowInsecure = profileItem.insecure ?: false,
                sni = profileItem.sni ?: "",
                fingerprint = profileItem.fingerPrint ?: "",
                alpn = profileItem.alpn ?: "",
                publicKey = profileItem.publicKey ?: "",
                shortId = profileItem.shortId ?: "",
                spiderX = profileItem.spiderX ?: "",
                flow = profileItem.flow ?: "",
                method = profileItem.method ?: "",
                password = profileItem.password ?: "",
                username = profileItem.username ?: "",
                secretKey = profileItem.secretKey ?: "",
                localAddress = profileItem.localAddress ?: "",
                mtu = profileItem.mtu ?: 1420,
                reserved = profileItem.reserved ?: "",
                createdAt = profileItem.addedTime,
                updatedAt = System.currentTimeMillis(),
                host = profileItem.host ?: "",
                insecure = profileItem.insecure ?: false
            )
        }
    }
    
    /**
     * Convert FirebaseServerModel to ProfileItem
     */
    fun toProfileItem(): ProfileItem {
        val configType = try {
            EConfigType.valueOf(this.configType.uppercase())
        } catch (e: Exception) {
            EConfigType.VMESS
        }

        return ProfileItem(
            configType = configType,
            remarks = this.name,
            server = this.server,
            serverPort = this.serverPort,
            password = this.password.ifEmpty { this.userId }, // Use userId as password for VMESS/VLESS
            method = this.method.ifEmpty { this.security },
            flow = this.flow,
            username = this.username,
            network = this.network,
            headerType = this.headerType,
            host = this.requestHost,
            path = this.path,
            security = this.streamSecurity,
            sni = this.sni,
            alpn = this.alpn,
            fingerPrint = this.fingerprint,
            insecure = this.allowInsecure,
            publicKey = this.publicKey,
            shortId = this.shortId,
            spiderX = this.spiderX,
            secretKey = this.secretKey,
            localAddress = this.localAddress,
            mtu = this.mtu,
            reserved = this.reserved,
            addedTime = this.createdAt
        )
    }
    
    /**
     * Convert to ServerConfig for local storage
     */
    fun toServerConfig(): ServerConfig {
        val configType = try {
            EConfigType.valueOf(this.configType.uppercase())
        } catch (e: Exception) {
            EConfigType.VMESS
        }
        
        return ServerConfig.create(configType).apply {
            remarks = <EMAIL>
        }
    }
}

/**
 * Firebase Server Category Model
 */
data class FirebaseServerCategory(
    var id: String = "",
    var name: String = "",
    var description: String = "",
    var color: String = "",
    var sortOrder: Int = 0,
    var isActive: Boolean = true,
    var createdAt: Long = 0
)

/**
 * Firebase Server Statistics Model
 */
data class FirebaseServerStats(
    var serverId: String = "",
    var totalConnections: Long = 0,
    var activeConnections: Long = 0,
    var totalDataTransfer: Long = 0,
    var averagePing: Long = 0,
    var uptime: Long = 0,
    var lastPingTest: Long = 0,
    var isOnline: Boolean = true,
    var updatedAt: Long = 0
)
