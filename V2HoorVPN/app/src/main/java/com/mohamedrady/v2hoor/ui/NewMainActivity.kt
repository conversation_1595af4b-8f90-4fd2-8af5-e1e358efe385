package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.net.VpnService
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.ArrayAdapter
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.ActionBarDrawerToggle
import androidx.core.content.ContextCompat
import androidx.core.view.GravityCompat
import androidx.lifecycle.lifecycleScope
import com.google.android.material.navigation.NavigationView
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityMainNewBinding
import com.mohamedrady.v2hoor.dto.EConfigType
import com.mohamedrady.v2hoor.dto.ProfileItem
import com.mohamedrady.v2hoor.extension.toast
import android.util.Log
import com.mohamedrady.v2hoor.firebase.FirestoreManagerSimple
import com.mohamedrady.v2hoor.handler.AngConfigManager
import com.mohamedrady.v2hoor.handler.MmkvManager
import com.mohamedrady.v2hoor.service.V2RayServiceManager
import com.mohamedrady.v2hoor.viewmodel.MainViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * New Modern Main Activity with improved UI and circular connection button
 */
class NewMainActivity : BaseActivity(), NavigationView.OnNavigationItemSelectedListener {
    
    private val binding by lazy { ActivityMainNewBinding.inflate(layoutInflater) }
    private val mainViewModel: MainViewModel by viewModels()
    
    private val requestVpnPermission = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        if (it.resultCode == RESULT_OK) {
            startV2Ray()
        }
    }
    
    private var serverList = mutableListOf<ProfileItem>()
    private lateinit var serverAdapter: ArrayAdapter<String>
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Check authentication first
        if (!FirebaseAuthManager.isLoggedIn() && !MmkvManager.decodeBool("guest_mode", false)) {
            startActivity(Intent(this, LoginActivity::class.java))
            finish()
            return
        }
        
        setContentView(binding.root)
        
        setupToolbar()
        setupNavigationDrawer()
        setupConnectionButton()
        setupServerSelection()
        setupQuickActions()
        setupViewModel()
        setupNavigationMenu()
        
        loadServers()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            title = getString(R.string.app_name)
        }
    }
    
    private fun setupNavigationDrawer() {
        val toggle = ActionBarDrawerToggle(
            this, binding.drawerLayout, binding.toolbar,
            R.string.navigation_drawer_open, R.string.navigation_drawer_close
        )
        binding.drawerLayout.addDrawerListener(toggle)
        toggle.syncState()
        binding.navView.setNavigationItemSelectedListener(this)
        
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (binding.drawerLayout.isDrawerOpen(GravityCompat.START)) {
                    binding.drawerLayout.closeDrawer(GravityCompat.START)
                } else {
                    isEnabled = false
                    onBackPressedDispatcher.onBackPressed()
                    isEnabled = true
                }
            }
        })
    }
    
    private fun setupConnectionButton() {
        binding.btnConnection.setOnClickListener {
            if (mainViewModel.isRunning.value == true) {
                // Disconnect
                V2RayServiceManager.stopVService(this)
            } else {
                // Connect
                if (MmkvManager.getSelectServer().isNullOrEmpty()) {
                    toast("يرجى اختيار سيرفر أولاً")
                    return@setOnClickListener
                }
                
                if (MmkvManager.decodeSettingsString(AppConfig.PREF_MODE) == AppConfig.VPN) {
                    val intent = VpnService.prepare(this)
                    if (intent == null) {
                        startV2Ray()
                    } else {
                        requestVpnPermission.launch(intent)
                    }
                } else {
                    startV2Ray()
                }
            }
        }
    }
    
    private fun setupServerSelection() {
        serverAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, mutableListOf<String>())
        binding.actvServerSelection.setAdapter(serverAdapter)
        
        binding.actvServerSelection.setOnItemClickListener { _, _, position, _ ->
            if (position < serverList.size) {
                val selectedServer = serverList[position]
                selectServer(selectedServer)
            }
        }
        
        binding.switchAutoSelection.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                selectBestServer()
            }
        }
    }
    
    private fun setupQuickActions() {
        binding.btnServerList.setOnClickListener {
            startActivity(Intent(this, MainActivity::class.java).apply {
                putExtra("show_server_list", true)
            })
        }
        
        binding.btnSettings.setOnClickListener {
            startActivity(Intent(this, SettingsActivity::class.java))
        }
    }
    
    private fun setupViewModel() {
        mainViewModel.isRunning.observe(this) { isRunning ->
            updateConnectionUI(isRunning)
        }
        
        mainViewModel.startListenBroadcast()
        mainViewModel.initAssets(assets)
    }
    
    private fun setupNavigationMenu() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val currentUser = com.google.firebase.auth.FirebaseAuth.getInstance().currentUser
                val isLoggedIn = FirebaseAuthManager.isLoggedIn()
                val isGuestMode = MmkvManager.decodeBool("guest_mode", false)
                
                val isAdmin = currentUser?.email == "<EMAIL>" || 
                             FirebaseAuthManager.isCurrentUserAdmin()
                
                launch(Dispatchers.Main) {
                    val menu = binding.navView.menu
                    val adminPanelItem = menu.findItem(R.id.admin_panel)
                    adminPanelItem?.isVisible = isLoggedIn && !isGuestMode && isAdmin
                }
            } catch (e: Exception) {
                val currentUser = com.google.firebase.auth.FirebaseAuth.getInstance().currentUser
                val isLoggedIn = FirebaseAuthManager.isLoggedIn()
                val isGuestMode = MmkvManager.decodeBool("guest_mode", false)
                
                launch(Dispatchers.Main) {
                    val menu = binding.navView.menu
                    val adminPanelItem = menu.findItem(R.id.admin_panel)
                    val isAdmin = currentUser?.email == "<EMAIL>"
                    adminPanelItem?.isVisible = isLoggedIn && !isGuestMode && isAdmin
                }
            }
        }
    }
    
    private fun loadServers() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Load local servers first
                val serverGuids = MmkvManager.decodeServerList()
                serverList.clear()

                serverGuids.forEach { guid ->
                    MmkvManager.decodeServerConfig(guid)?.let { config ->
                        serverList.add(config)
                    }
                }

                launch(Dispatchers.Main) {
                    updateServerDropdown()
                    updateSelectedServerInfo()
                    if (serverList.isEmpty()) {
                        // Show placeholder servers if no servers found
                        val serverNames = listOf("سيرفر 1", "سيرفر 2", "سيرفر 3")
                        serverAdapter.clear()
                        serverAdapter.addAll(serverNames)
                        serverAdapter.notifyDataSetChanged()
                        toast("لا توجد سيرفرات متاحة")
                    } else {
                        toast("تم تحميل ${serverList.size} سيرفر")
                    }
                }

                // Firestore sync will be implemented later
                Log.d("NewMainActivity", "Firestore sync placeholder")
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    // Fallback to placeholder servers if loading fails
                    val serverNames = listOf("سيرفر 1", "سيرفر 2", "سيرفر 3")
                    serverAdapter.clear()
                    serverAdapter.addAll(serverNames)
                    serverAdapter.notifyDataSetChanged()
                    updateSelectedServerInfo()
                    toast("خطأ في تحميل السيرفرات: ${e.message}")
                }
            }
        }
    }

    private fun updateServerDropdown() {
        val serverNames = serverList.map { config ->
            config.remarks.ifEmpty { "سيرفر ${serverList.indexOf(config) + 1}" }
        }
        serverAdapter.clear()
        serverAdapter.addAll(serverNames)
        serverAdapter.notifyDataSetChanged()
    }

    private fun selectServer(server: ProfileItem) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Find the server GUID from the server list
                val serverGuids = MmkvManager.decodeServerList()
                val serverGuid = serverGuids.find { guid ->
                    MmkvManager.decodeServerConfig(guid)?.let { config ->
                        config.remarks == server.remarks && config.server == server.server
                    } ?: false
                }

                if (serverGuid != null) {
                    MmkvManager.setSelectServer(serverGuid)
                }

                launch(Dispatchers.Main) {
                    val serverName = server.remarks.ifEmpty { "سيرفر محدد" }
                    binding.actvServerSelection.setText(serverName, false)
                    updateSelectedServerInfo()
                    toast("تم اختيار السيرفر: $serverName")
                }
            } catch (e: Exception) {
                launch(Dispatchers.Main) {
                    toast("خطأ في اختيار السيرفر: ${e.message}")
                }
            }
        }
    }



    private fun updateSelectedServerInfo() {
        val selectedGuid = MmkvManager.getSelectServer()
        val selectedServer = if (!selectedGuid.isNullOrEmpty()) {
            MmkvManager.decodeServerConfig(selectedGuid)
        } else {
            null
        }

        binding.tvSelectedServer.text = selectedServer?.remarks ?: "لم يتم اختيار سيرفر"

        // Update dropdown selection if server is selected
        if (selectedServer != null) {
            val serverName = selectedServer.remarks.ifEmpty { "سيرفر محدد" }
            binding.actvServerSelection.setText(serverName, false)
        }
    }

    private fun selectBestServer() {
        lifecycleScope.launch(Dispatchers.Main) {
            try {
                toast("جاري البحث عن أفضل سيرفر...")

                if (serverList.isNotEmpty()) {
                    // Simple best server selection - pick first available
                    val bestServer = serverList.first()
                    selectServer(bestServer)
                    binding.actvServerSelection.setText(bestServer.remarks, false)
                    toast("تم اختيار أفضل سيرفر: ${bestServer.remarks}")
                } else {
                    toast("لا توجد سيرفرات متاحة")
                }
            } catch (e: Exception) {
                toast("خطأ في الاختيار التلقائي: ${e.message}")
            }
        }
    }

    private fun updateConnectionUI(isRunning: Boolean) {
        if (isRunning) {
            binding.btnConnection.apply {
                text = "قطع الاتصال"
                isSelected = true
                setBackgroundColor(ContextCompat.getColor(this@NewMainActivity, R.color.connection_button_connected))
            }
            binding.tvConnectionStatus.text = "متصل"
            binding.tvConnectionStatus.setTextColor(ContextCompat.getColor(this, R.color.color_success))
        } else {
            binding.btnConnection.apply {
                text = "اتصال"
                isSelected = false
                setBackgroundColor(ContextCompat.getColor(this@NewMainActivity, R.color.connection_button_disconnected))
            }
            binding.tvConnectionStatus.text = "غير متصل"
            binding.tvConnectionStatus.setTextColor(ContextCompat.getColor(this, R.color.color_text_secondary))
        }
    }
    
    private fun startV2Ray() {
        V2RayServiceManager.startVService(this)
    }
    
    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.nav_home -> {
                // Already on home page, just close drawer
                binding.drawerLayout.closeDrawer(GravityCompat.START)
                return true
            }
            R.id.servers_management -> {
                startActivity(Intent(this, MainActivity::class.java).apply {
                    putExtra("show_server_list", true)
                })
            }
            R.id.settings -> {
                startActivity(Intent(this, SettingsActivity::class.java))
            }
            R.id.admin_panel -> {
                startActivity(Intent(this, AdminPanelActivity::class.java))
            }
            R.id.server_admin -> {
                startActivity(Intent(this, ServerAdminActivity::class.java))
            }
            R.id.sub_setting -> {
                startActivity(Intent(this, SubSettingActivity::class.java))
            }
            R.id.per_app_proxy_settings -> {
                startActivity(Intent(this, PerAppProxyActivity::class.java))
            }
            R.id.routing_setting -> {
                startActivity(Intent(this, RoutingSettingActivity::class.java))
            }
            R.id.user_asset_setting -> {
                startActivity(Intent(this, UserAssetActivity::class.java))
            }
            R.id.logcat -> {
                startActivity(Intent(this, LogcatActivity::class.java))
            }
            R.id.check_for_update -> {
                startActivity(Intent(this, CheckUpdateActivity::class.java))
            }
            R.id.about -> {
                startActivity(Intent(this, AboutActivity::class.java))
            }
        }
        binding.drawerLayout.closeDrawer(GravityCompat.START)
        return true
    }
    
    private fun performLogout() {
        lifecycleScope.launch(Dispatchers.IO) {
            val success = FirebaseAuthManager.signOut()
            
            launch(Dispatchers.Main) {
                if (success) {
                    startActivity(Intent(this@NewMainActivity, LoginActivity::class.java).apply {
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    })
                    finish()
                } else {
                    toast("فشل في تسجيل الخروج")
                }
            }
        }
    }
    
    override fun onResume() {
        super.onResume()
        loadServers()
    }
}
