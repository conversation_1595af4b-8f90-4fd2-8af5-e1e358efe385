package com.mohamedrady.v2hoor.firebase

/**
 * Firebase User Model for user management
 */
data class FirebaseUserModel(
    var uid: String = "",
    var email: String = "",
    var username: String = "",
    var displayName: String = "",
    var phoneNumber: String = "",
    var profileImageUrl: String = "",
    var isActive: Boolean = true,
    var isAdmin: Boolean = false,
    var role: String = "user", // user, admin, moderator
    var subscription: UserSubscription = UserSubscription(),
    var assignedServers: List<String> = emptyList(),
    var dataUsage: UserDataUsage = UserDataUsage(),
    var settings: UserSettings = UserSettings(),
    var createdAt: Long = 0,
    var updatedAt: Long = 0,
    var lastLoginAt: Long = 0,
    var loginCount: Long = 0,
    var deviceInfo: DeviceInfo = DeviceInfo(),
    var notes: String = ""
) {
    companion object {
        const val ROLE_USER = "user"
        const val ROLE_ADMIN = "admin"
        const val ROLE_MODERATOR = "moderator"
        const val ROLE_GUEST = "guest"

        /**
         * Create default admin user
         */
        fun createDefaultAdmin(): FirebaseUserModel {
            return FirebaseUserModel(
                uid = "admin_default",
                email = "<EMAIL>",
                username = "admin",
                displayName = "مدير النظام",
                isActive = true,
                isAdmin = true,
                role = "admin",
                subscription = UserSubscription(
                    planType = "unlimited",
                    planName = "خطة إدارية",
                    expiryDate = Long.MAX_VALUE,
                    dataLimit = -1, // Unlimited
                    isActive = true
                ),
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )
        }
        
        /**
         * Create guest user
         */
        fun createGuestUser(): FirebaseUserModel {
            return FirebaseUserModel(
                uid = "guest_${System.currentTimeMillis()}",
                email = "",
                username = "guest",
                displayName = "مستخدم ضيف",
                isActive = true,
                isAdmin = false,
                role = "guest",
                subscription = UserSubscription(
                    planType = "free",
                    planName = "خطة مجانية",
                    expiryDate = System.currentTimeMillis() + (30 * 24 * 60 * 60 * 1000L), // 30 days
                    dataLimit = 1024 * 1024 * 1024, // 1GB
                    isActive = true
                ),
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )
        }
    }

    /**
     * Check if user is admin
     */
    fun isAdmin(): Boolean {
        return isAdmin || role == ROLE_ADMIN
    }
}

/**
 * User Subscription Model
 */
data class UserSubscription(
    var planType: String = "free", // free, premium, unlimited
    var planName: String = "خطة مجانية",
    var expiryDate: Long = 0,
    var dataLimit: Long = 0, // in bytes, -1 for unlimited
    var dataUsed: Long = 0,
    var isActive: Boolean = true,
    var autoRenew: Boolean = false,
    var paymentMethod: String = "",
    var lastPaymentDate: Long = 0,
    var nextBillingDate: Long = 0,
    var subscriptionId: String = "",
    var features: List<String> = emptyList(),
    var maxServers: Int = 3,
    var maxDevices: Int = 1,
    var bandwidthLimit: Long = 1024 * 1024 * 1024, // 1GB in bytes
    var speedLimit: Int = 10, // Mbps
    var endDate: Long = 0,
    var isSubscriptionActive: Boolean = true
) {
    /**
     * Check if subscription is expired
     */
    fun isExpired(): Boolean {
        return expiryDate > 0 && System.currentTimeMillis() > expiryDate
    }
    
    /**
     * Check if data limit is exceeded
     */
    fun isDataLimitExceeded(): Boolean {
        return dataLimit > 0 && dataUsed >= dataLimit
    }
    
    /**
     * Get remaining data in bytes
     */
    fun getRemainingData(): Long {
        return if (dataLimit <= 0) -1 else (dataLimit - dataUsed).coerceAtLeast(0)
    }
    
    /**
     * Get data usage percentage
     */
    fun getDataUsagePercentage(): Float {
        return if (dataLimit <= 0) 0f else (dataUsed.toFloat() / dataLimit.toFloat() * 100f).coerceAtMost(100f)
    }

    /**
     * Check if subscription is expiring soon (within 7 days)
     */
    fun isExpiringSoon(): Boolean {
        if (endDate <= 0) return false
        val sevenDaysFromNow = System.currentTimeMillis() + (7 * 24 * 60 * 60 * 1000L)
        return endDate <= sevenDaysFromNow && endDate > System.currentTimeMillis()
    }
}

/**
 * User Data Usage Model
 */
data class UserDataUsage(
    var totalUpload: Long = 0,
    var totalDownload: Long = 0,
    var dailyUsage: Map<String, Long> = emptyMap(), // date -> bytes
    var monthlyUsage: Map<String, Long> = emptyMap(), // month -> bytes
    var lastResetDate: Long = 0,
    var currentSessionUpload: Long = 0,
    var currentSessionDownload: Long = 0,
    var sessionStartTime: Long = 0
) {
    /**
     * Get total data usage
     */
    fun getTotalUsage(): Long {
        return totalUpload + totalDownload
    }
    
    /**
     * Get current session total
     */
    fun getCurrentSessionTotal(): Long {
        return currentSessionUpload + currentSessionDownload
    }
}

/**
 * User Settings Model
 */
data class UserSettings(
    var language: String = "ar",
    var theme: String = "auto", // light, dark, auto
    var notifications: Boolean = true,
    var autoConnect: Boolean = false,
    var preferredServer: String = "",
    var connectionTimeout: Int = 30,
    var retryAttempts: Int = 3,
    var killSwitch: Boolean = false,
    var dnsSettings: String = "auto",
    var proxySettings: Map<String, String> = emptyMap()
)

/**
 * Device Information Model
 */
data class DeviceInfo(
    var deviceId: String = "",
    var deviceName: String = "",
    var deviceType: String = "", // phone, tablet, tv
    var osVersion: String = "",
    var appVersion: String = "",
    var lastIpAddress: String = "",
    var lastLocation: String = "",
    var registrationToken: String = "" // for push notifications
)
