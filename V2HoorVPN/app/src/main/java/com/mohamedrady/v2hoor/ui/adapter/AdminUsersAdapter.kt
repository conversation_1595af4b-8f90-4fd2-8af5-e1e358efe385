package com.mohamedrady.v2hoor.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.firebase.FirebaseUserModel
import com.mohamedrady.v2hoor.ui.AdminPanelActivity
import com.mohamedrady.v2hoor.utils.SuperAdminManager
import android.content.Intent
import com.mohamedrady.v2hoor.ui.AssignServersActivity
import java.text.SimpleDateFormat
import java.util.*

/**
 * Interface for user actions
 */
interface UserActionListener {
    fun editUser(user: FirebaseUserModel)
    fun toggleUserStatus(user: FirebaseUserModel)
    fun assignServersToUser(user: FirebaseUserModel)
}

/**
 * Adapter for displaying users in admin panel
 */
class AdminUsersAdapter(
    private val activity: Any,
    private val listener: UserActionListener? = null
) : RecyclerView.Adapter<AdminUsersAdapter.UserViewHolder>() {

    private var users = mutableListOf<FirebaseUserModel>()
    private val dateFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())

    fun updateUsers(newUsers: List<FirebaseUserModel>) {
        users.clear()
        users.addAll(newUsers)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_admin_user, parent, false)
        return UserViewHolder(view)
    }

    override fun onBindViewHolder(holder: UserViewHolder, position: Int) {
        val user = users[position]
        holder.bind(user)
    }

    override fun getItemCount(): Int = users.size

    inner class UserViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvUserName: TextView = itemView.findViewById(R.id.tv_user_name)
        private val tvUserEmail: TextView = itemView.findViewById(R.id.tv_user_email)
        private val tvUserRole: TextView = itemView.findViewById(R.id.tv_user_role)
        private val tvSubscriptionType: TextView = itemView.findViewById(R.id.tv_subscription_type)
        private val tvSubscriptionExpiry: TextView = itemView.findViewById(R.id.tv_subscription_expiry)
        private val ivUserStatus: ImageView = itemView.findViewById(R.id.iv_user_status)
        private val ivUserRole: ImageView = itemView.findViewById(R.id.iv_user_role)

        fun bind(user: FirebaseUserModel) {
            tvUserName.text = user.displayName.ifEmpty { user.username }
            tvUserEmail.text = user.email
            
            // Set role
            if (SuperAdminManager.isSuperAdmin(user)) {
                tvUserRole.text = "مدير عام"
                tvUserRole.setTextColor(itemView.context.getColor(R.color.color_error))
                ivUserRole.setImageResource(R.drawable.ic_admin_24dp)
                ivUserRole.setColorFilter(itemView.context.getColor(R.color.color_error))
            } else if (user.isAdmin || user.role == FirebaseUserModel.ROLE_ADMIN) {
                tvUserRole.text = "مدير"
                tvUserRole.setTextColor(itemView.context.getColor(R.color.color_error))
                ivUserRole.setImageResource(R.drawable.ic_admin_24dp)
                ivUserRole.setColorFilter(itemView.context.getColor(R.color.color_error))
            } else {
                tvUserRole.text = "مستخدم"
                tvUserRole.setTextColor(itemView.context.getColor(R.color.color_text_secondary))
                ivUserRole.setImageResource(R.drawable.ic_person_24dp)
                ivUserRole.setColorFilter(itemView.context.getColor(R.color.color_text_secondary))
            }
            
            // Set status
            if (user.isActive) {
                ivUserStatus.setImageResource(R.drawable.ic_check_circle_24dp)
                ivUserStatus.setColorFilter(itemView.context.getColor(R.color.color_success))
            } else {
                ivUserStatus.setImageResource(R.drawable.ic_cancel_24dp)
                ivUserStatus.setColorFilter(itemView.context.getColor(R.color.color_error))
            }
            
            // Set subscription
            val subscription = user.subscription
            tvSubscriptionType.text = when (subscription.planType) {
                "free" -> "مجاني"
                "premium" -> "مميز"
                "vip" -> "VIP"
                else -> subscription.planType
            }
            
            // Set subscription color
            when (subscription.planType) {
                "free" -> tvSubscriptionType.setTextColor(itemView.context.getColor(R.color.color_text_secondary))
                "premium" -> tvSubscriptionType.setTextColor(itemView.context.getColor(R.color.color_warning))
                "vip" -> tvSubscriptionType.setTextColor(itemView.context.getColor(R.color.color_success))
            }
            
            // Set expiry date
            if (subscription.endDate > 0) {
                val expiryDate = Date(subscription.endDate)
                tvSubscriptionExpiry.text = "ينتهي: ${dateFormat.format(expiryDate)}"
                
                if (subscription.isExpired()) {
                    tvSubscriptionExpiry.setTextColor(itemView.context.getColor(R.color.color_error))
                } else if (subscription.isExpiringSoon()) {
                    tvSubscriptionExpiry.setTextColor(itemView.context.getColor(R.color.color_warning))
                } else {
                    tvSubscriptionExpiry.setTextColor(itemView.context.getColor(R.color.color_success))
                }
            } else {
                tvSubscriptionExpiry.text = "بدون انتهاء"
                tvSubscriptionExpiry.setTextColor(itemView.context.getColor(R.color.color_info))
            }
            
            // Set click listeners
            itemView.setOnClickListener {
                listener?.editUser(user) ?: run {
                    if (activity is AdminPanelActivity) {
                        activity.editUser(user)
                    }
                }
            }
            
            itemView.setOnLongClickListener {
                showUserOptions(user)
                true
            }
        }
        
        private fun showUserOptions(user: FirebaseUserModel) {
            val options = arrayOf(
                "تعديل المستخدم",
                if (user.isActive) "إلغاء تفعيل" else "تفعيل",
                "ربط السيرفرات",
                "عرض الإحصائيات"
            )
            
            androidx.appcompat.app.AlertDialog.Builder(itemView.context)
                .setTitle("خيارات المستخدم")
                .setItems(options) { _, which ->
                    when (which) {
                        0 -> listener?.editUser(user) ?: run {
                            if (activity is AdminPanelActivity) {
                                activity.editUser(user)
                            }
                        }
                        1 -> listener?.toggleUserStatus(user) ?: run {
                            if (activity is AdminPanelActivity) {
                                activity.toggleUserStatus(user)
                            }
                        }
                        2 -> listener?.assignServersToUser(user) ?: run {
                            // Direct navigation to AssignServersActivity
                            val intent = Intent(itemView.context, AssignServersActivity::class.java).apply {
                                putExtra("user_id", user.uid)
                                putExtra("user_name", user.displayName)
                                putExtra("user_email", user.email)
                            }
                            itemView.context.startActivity(intent)
                        }
                        3 -> showUserStats(user)
                    }
                }
                .show()
        }
        
        private fun showUserStats(user: FirebaseUserModel) {
            val message = buildString {
                append("معلومات المستخدم:\n\n")
                append("الاسم: ${user.displayName}\n")
                append("البريد: ${user.email}\n")
                append("الدور: ${
                    when {
                        SuperAdminManager.isSuperAdmin(user) -> "مدير عام"
                        user.isAdmin || user.role == FirebaseUserModel.ROLE_ADMIN -> "مدير"
                        else -> "مستخدم"
                    }
                }\n")
                append("تاريخ التسجيل: ${dateFormat.format(Date(user.createdAt))}\n")
                append("آخر دخول: ${if (user.lastLoginAt > 0) dateFormat.format(Date(user.lastLoginAt)) else "لم يسجل دخول"}\n")
                append("نوع الاشتراك: ${user.subscription.planName}\n")
                append("الحالة: ${if (user.isActive) "نشط" else "غير نشط"}\n")
            }
            
            androidx.appcompat.app.AlertDialog.Builder(itemView.context)
                .setTitle("إحصائيات المستخدم")
                .setMessage(message)
                .setPositiveButton("موافق", null)
                .show()
        }
    }
}
