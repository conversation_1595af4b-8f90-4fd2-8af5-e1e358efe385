<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_background"
    tools:context=".ui.StatisticsActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay"
            app:title="إحصائيات النظام"
            app:titleTextColor="@android:color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Progress Bar -->
                <ProgressBar
                    android:id="@+id/progress_bar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_margin="32dp"
                    android:visibility="gone" />

                <!-- Statistics Layout -->
                <LinearLayout
                    android:id="@+id/layout_statistics"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <!-- User Statistics Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="16dp"
                                android:drawableStart="@drawable/ic_person_24dp"
                                android:drawablePadding="8dp"
                                android:text="إحصائيات المستخدمين"
                                android:textColor="@color/color_text_primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <!-- Total Users -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="12dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="إجمالي المستخدمين"
                                    android:textColor="@color/color_text_secondary"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_total_users"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/color_text_primary"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="1,234" />

                            </LinearLayout>

                            <!-- Active Users -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="12dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="المستخدمين النشطين"
                                    android:textColor="@color/color_text_secondary"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_active_users"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/colorPrimary"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="987" />

                                <TextView
                                    android:id="@+id/tv_active_user_percentage"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="8dp"
                                    android:textColor="@color/colorPrimary"
                                    android:textSize="12sp"
                                    tools:text="80%" />

                            </LinearLayout>

                            <ProgressBar
                                android:id="@+id/progress_active_users"
                                style="?android:attr/progressBarStyleHorizontal"
                                android:layout_width="match_parent"
                                android:layout_height="8dp"
                                android:layout_marginBottom="12dp"
                                android:max="100"
                                android:progressTint="@color/colorPrimary"
                                tools:progress="80" />

                            <!-- Admin Users -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="12dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="المديرين"
                                    android:textColor="@color/color_text_secondary"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_admin_users"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/color_error"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="5" />

                            </LinearLayout>

                            <!-- Premium Users -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="المشتركين المميزين"
                                    android:textColor="@color/color_text_secondary"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_premium_users"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/color_success"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="456" />

                            </LinearLayout>

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Server Statistics Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="16dp"
                                android:drawableStart="@drawable/ic_server_24dp"
                                android:drawablePadding="8dp"
                                android:text="إحصائيات السيرفرات"
                                android:textColor="@color/color_text_primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <!-- Total Servers -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="12dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="إجمالي السيرفرات"
                                    android:textColor="@color/color_text_secondary"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_total_servers"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/color_text_primary"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="25" />

                            </LinearLayout>

                            <!-- Active Servers -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="12dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="السيرفرات النشطة"
                                    android:textColor="@color/color_text_secondary"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_active_servers"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/color_success"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="20" />

                                <TextView
                                    android:id="@+id/tv_active_server_percentage"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="8dp"
                                    android:textColor="@color/color_success"
                                    android:textSize="12sp"
                                    tools:text="80%" />

                            </LinearLayout>

                            <ProgressBar
                                android:id="@+id/progress_active_servers"
                                style="?android:attr/progressBarStyleHorizontal"
                                android:layout_width="match_parent"
                                android:layout_height="8dp"
                                android:max="100"
                                android:progressTint="@color/color_success"
                                tools:progress="80" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Data Usage Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="16dp"
                                android:drawableStart="@drawable/ic_cloud_download_24dp"
                                android:drawablePadding="8dp"
                                android:text="استهلاك البيانات"
                                android:textColor="@color/color_text_primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="إجمالي البيانات المستهلكة"
                                    android:textColor="@color/color_text_secondary"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_total_data_usage"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/colorAccent"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="1.2 TB" />

                            </LinearLayout>

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Empty State -->
                <LinearLayout
                    android:id="@+id/layout_empty"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="32dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:layout_marginBottom="16dp"
                        android:src="@drawable/ic_about_24dp"
                        android:tint="@color/color_text_secondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="لا توجد إحصائيات متاحة"
                        android:textColor="@color/color_text_secondary"
                        android:textSize="16sp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
