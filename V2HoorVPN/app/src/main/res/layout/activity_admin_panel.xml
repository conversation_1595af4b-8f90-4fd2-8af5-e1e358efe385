<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.AdminPanelActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/gradient_primary"
        android:elevation="4dp"
        app:title="@string/admin_panel"
        app:titleTextColor="@android:color/white" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:background="@color/background_light">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/padding_spacing_dp16">

                <!-- Statistics Cards -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/padding_spacing_dp16"
                    android:text="إحصائيات عامة"
                    android:textAppearance="@style/TextAppearance.AppCompat.Large"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/padding_spacing_dp16"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/padding_spacing_dp8"
                        android:layout_weight="1"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@android:color/white">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="@dimen/padding_spacing_dp16">

                            <TextView
                                android:id="@+id/tv_total_users"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textAppearance="@style/TextAppearance.AppCompat.Display1"
                                android:textColor="@color/colorPrimary"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_users"
                                android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                                android:textColor="@color/color_text_secondary" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/padding_spacing_dp8"
                        android:layout_weight="1"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@android:color/white">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="@dimen/padding_spacing_dp16">

                            <TextView
                                android:id="@+id/tv_active_users"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textAppearance="@style/TextAppearance.AppCompat.Display1"
                                android:textColor="@color/color_success"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/active_users"
                                android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                                android:textColor="@color/color_text_secondary" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/padding_spacing_dp24"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/padding_spacing_dp8"
                        android:layout_weight="1"
                        app:cardCornerRadius="@dimen/card_corner_radius"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="@dimen/padding_spacing_dp16">

                            <TextView
                                android:id="@+id/tv_premium_users"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textAppearance="@style/TextAppearance.AppCompat.Display1"
                                android:textColor="@color/color_warning"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/premium_users"
                                android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                                android:textColor="@color/color_text_secondary" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/padding_spacing_dp8"
                        android:layout_weight="1"
                        app:cardCornerRadius="@dimen/card_corner_radius"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="@dimen/padding_spacing_dp16">

                            <TextView
                                android:id="@+id/tv_total_servers"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textAppearance="@style/TextAppearance.AppCompat.Display1"
                                android:textColor="@color/color_info"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_servers"
                                android:textAppearance="@style/TextAppearance.AppCompat.Caption"
                                android:textColor="@color/color_text_secondary" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Management Cards -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/padding_spacing_dp16"
                    android:text="إدارة النظام"
                    android:textAppearance="@style/TextAppearance.AppCompat.Large"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/padding_spacing_dp16"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_server_management"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/padding_spacing_dp8"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@android:color/white">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="@dimen/padding_spacing_dp24">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_marginBottom="@dimen/padding_spacing_dp8"
                                android:src="@drawable/ic_server_24dp"
                                android:tint="@color/colorPrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="إدارة السيرفرات"
                                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_user_management"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/padding_spacing_dp8"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground"
                        app:cardCornerRadius="@dimen/card_corner_radius"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="@dimen/padding_spacing_dp24">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_marginBottom="@dimen/padding_spacing_dp8"
                                android:src="@drawable/ic_person_24dp"
                                android:tint="@color/color_success" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/user_management"
                                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Second Row of Management Cards -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/padding_spacing_dp16"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_subscription_management"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/padding_spacing_dp8"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground"
                        app:cardCornerRadius="@dimen/card_corner_radius"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="@dimen/padding_spacing_dp24">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_marginBottom="@dimen/padding_spacing_dp8"
                                android:src="@drawable/ic_diamond_24dp"
                                android:tint="@color/color_warning" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="إدارة الاشتراكات"
                                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_assign_servers"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/padding_spacing_dp8"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground"
                        app:cardCornerRadius="@dimen/card_corner_radius"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="@dimen/padding_spacing_dp24">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_marginBottom="@dimen/padding_spacing_dp8"
                                android:src="@drawable/ic_link_24dp"
                                android:tint="@color/color_info" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تخصيص السيرفرات"
                                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_statistics"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/padding_spacing_dp8"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@android:color/white">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="@dimen/padding_spacing_dp24">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_marginBottom="@dimen/padding_spacing_dp8"
                                android:src="@drawable/ic_about_24dp"
                                android:tint="@color/colorAccent" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="الإحصائيات"
                                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Third Row - Admin Settings -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/padding_spacing_dp24"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_admin_settings"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/padding_spacing_dp8"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground"
                        app:cardCornerRadius="@dimen/card_corner_radius"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="@dimen/padding_spacing_dp24">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_marginBottom="@dimen/padding_spacing_dp8"
                                android:src="@drawable/ic_settings_24dp"
                                android:tint="@color/color_text_primary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="إعدادات المدير"
                                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Empty space for symmetry -->
                    <View
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/padding_spacing_dp8"
                        android:layout_weight="1" />

                </LinearLayout>

                <!-- Users List -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/padding_spacing_dp16"
                    android:text="المستخدمون"
                    android:textAppearance="@style/TextAppearance.AppCompat.Large"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view_users"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_admin_user" />

                <!-- Empty State -->
                <LinearLayout
                    android:id="@+id/layout_empty_users"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="@dimen/padding_spacing_dp32"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:layout_marginBottom="@dimen/padding_spacing_dp16"
                        android:alpha="0.5"
                        android:src="@drawable/ic_person_24dp"
                        android:tint="@color/color_text_secondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="لا يوجد مستخدمون"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textColor="@color/color_text_secondary" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- FAB -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_user"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="@dimen/padding_spacing_dp16"
        android:src="@drawable/ic_person_add_24dp"
        app:tint="@android:color/white" />

</LinearLayout>
