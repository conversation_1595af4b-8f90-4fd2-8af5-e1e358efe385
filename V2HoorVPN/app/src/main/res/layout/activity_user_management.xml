<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_background"
    tools:context=".ui.UserManagementActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay"
            app:title="إدارة المستخدمين"
            app:titleTextColor="@android:color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Search Bar -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="8dp"
                    app:boxBackgroundMode="none"
                    app:hintTextColor="@color/color_text_secondary"
                    app:startIconDrawable="@drawable/ic_search_24dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_text_search"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="البحث عن مستخدم..."
                        android:inputType="text"
                        android:textColor="@color/color_text_primary"
                        android:textColorHint="@color/color_text_secondary" />

                </com.google.android.material.textfield.TextInputLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Users List -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/colorPrimary"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="8dp"
                            android:src="@drawable/ic_person_24dp"
                            android:tint="@android:color/white" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="قائمة المستخدمين"
                            android:textColor="@android:color/white"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/text_view_user_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@android:color/white"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="4dp"
                            android:text="0"
                            android:textColor="@color/colorPrimary"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- Progress Bar -->
                    <ProgressBar
                        android:id="@+id/progress_bar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_margin="32dp"
                        android:visibility="gone" />

                    <!-- Empty State -->
                    <TextView
                        android:id="@+id/text_view_empty"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="32dp"
                        android:drawableTop="@drawable/ic_person_24dp"
                        android:drawablePadding="16dp"
                        android:gravity="center"
                        android:text="لا توجد مستخدمين"
                        android:textColor="@color/color_text_secondary"
                        android:textSize="16sp"
                        android:visibility="gone" />

                    <!-- Users RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_view_users"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clipToPadding="false"
                        android:nestedScrollingEnabled="false"
                        android:paddingBottom="8dp"
                        tools:listitem="@layout/item_admin_user" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_user"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add_24dp"
        app:backgroundTint="@color/colorPrimary"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
